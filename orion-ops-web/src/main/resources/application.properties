spring.application.name=orion-ops
server.port=9119
spring.data.cassandra.request.timeout=10000
spring.profiles.active=dev
# redis
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-wait=-1
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=0
spring.redis.timeout=3000
# datasource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
# mybatis
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.mapper-locations=classpath*:mapper/*Mapper.xml
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-not-delete-value=1
mybatis-plus.global-config.db-config.logic-delete-value=2
# tomcat
spring.servlet.multipart.max-file-size=2048MB
spring.servlet.multipart.max-request-size=2096MB
server.tomcat.connection-timeout=18000000
# log
spring.output.ansi.enabled=detect
logging.file.path=${user.home}/orion/logs/orion-ops
logging.file.name=${logging.file.path}/orion-ops.log
logging.logback.rollingpolicy.clean-history-on-start=false
logging.logback.rollingpolicy.file-name-pattern=${logging.file.path}/rolling/orion-ops-rolling-%d{yyyy-MM-dd}.%i.log
logging.logback.rollingpolicy.max-history=30
logging.logback.rollingpolicy.max-file-size=64MB
logging.logback.rollingpolicy.total-size-cap=0B
# app
app.version=1.3.1
login.token.header=O-Login-Token
value.mix.secret.key=${SECRET_KEY:orion_ops}
demo.mode=${DEMO_MODE:false}
# å¨å±æ¥å¿æå°åå¥ç¹
log.interceptor.expression=execution (* cn.orionsec.ops.controller.*.*(..)) && !@annotation(cn.orionsec.ops.annotation.IgnoreLog)
# å¨å±æ¥å¿æå°å¿½ç¥å­æ®µ
log.interceptor.ignore.fields=avatar,password,beforePassword,protectPassword,commandLine,metrics
# æ´é²æå¡éç½®
expose.api.access.header=accessToken
expose.api.access.secret=ops_access
# çæ§éç½®
machine.monitor.latest.version=1.1.1
machine.monitor.default.url=http://{}:9220
machine.monitor.default.access.header=accessToken
machine.monitor.default.access.token=agent_access
