<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.orionsec.ops</groupId>
        <artifactId>orion-ops-api</artifactId>
        <version>1.3.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <name>orion-ops-web</name>
    <artifactId>orion-ops-web</artifactId>
    <modelVersion>4.0.0</modelVersion>

    <dependencies>
        <!-- service -->
        <dependency>
            <groupId>cn.orionsec.ops</groupId>
            <artifactId>orion-ops-service</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- data -->
        <dependency>
            <groupId>cn.orionsec.ops</groupId>
            <artifactId>orion-ops-data</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- mapping -->
        <dependency>
            <groupId>cn.orionsec.ops</groupId>
            <artifactId>orion-ops-mapping</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- runner -->
        <dependency>
            <groupId>cn.orionsec.ops</groupId>
            <artifactId>orion-ops-runner</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- aspectj -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>

        <!-- swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>

        <!-- knife4j -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-micro-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-ui</artifactId>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**</include>
                </includes>
            </resource>
        </resources>

        <testResources>
            <testResource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**</include>
                </includes>
            </testResource>
        </testResources>

        <plugins>
            <!-- 普通打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- 分离依赖打包 -->
            <!--            <plugin>-->
            <!--                <groupId>org.springframework.boot</groupId>-->
            <!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
            <!--                <configuration>-->
            <!--                    <mainClass>cn.orionsec.ops.OrionApplication</mainClass>-->
            <!--                    <layout>ZIP</layout>-->
            <!--                    &lt;!&ndash; 注释后和依赖包一起打包 取消注释则不打包依赖 &ndash;&gt;-->
            <!--                    <includes>-->
            <!--                        <include>-->
            <!--                            <groupId>nothing</groupId>-->
            <!--                            <artifactId>nothing</artifactId>-->
            <!--                        </include>-->
            <!--                    </includes>-->
            <!--                    <excludes>-->
            <!--                        <exclude>-->
            <!--                            <groupId>org.projectlombok</groupId>-->
            <!--                            <artifactId>lombok</artifactId>-->
            <!--                        </exclude>-->
            <!--                    </excludes>-->
            <!--                </configuration>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <goals>-->
            <!--                            <goal>repackage</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
