<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.UserInfoDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.UserInfoDO">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="nickname" property="nickname"/>
        <result column="password" property="password"/>
        <result column="salt" property="salt"/>
        <result column="role_type" property="roleType"/>
        <result column="user_status" property="userStatus"/>
        <result column="failed_login_count" property="failedLoginCount"/>
        <result column="lock_status" property="lockStatus"/>
        <result column="avatar_pic" property="avatarPic"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="contact_email" property="contactEmail"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, username, nickname, password, salt, role_type, user_status, lock_status, failed_login_count, avatar_pic, contact_phone, contact_email, last_login_time, deleted, create_time, update_time
    </sql>

    <update id="updateLastLoginTime">
        UPDATE user_info
        SET last_login_time = now(),
        update_time = now()
        WHERE id = #{userId}
    </update>

</mapper>
