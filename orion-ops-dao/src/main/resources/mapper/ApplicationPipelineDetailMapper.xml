<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.ApplicationPipelineDetailDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.ApplicationPipelineDetailDO">
        <id column="id" property="id"/>
        <result column="pipeline_id" property="pipelineId"/>
        <result column="app_id" property="appId"/>
        <result column="profile_id" property="profileId"/>
        <result column="stage_type" property="stageType"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, pipeline_id, app_id, profile_id, stage_type, deleted, create_time, update_time
    </sql>

</mapper>
