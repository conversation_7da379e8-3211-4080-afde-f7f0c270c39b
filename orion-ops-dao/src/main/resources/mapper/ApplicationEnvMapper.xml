<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.ApplicationEnvDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.ApplicationEnvDO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="profile_id" property="profileId"/>
        <result column="attr_key" property="attrKey"/>
        <result column="attr_value" property="attrValue"/>
        <result column="system_env" property="systemEnv"/>
        <result column="description" property="description"/>
        <result column="deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, profile_id, attr_key, attr_value, system_env, description, deleted, create_time, update_time
    </sql>

    <select id="selectOneRel" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM application_env
        WHERE app_id = #{appId}
        AND profile_id = #{profileId}
        AND attr_key = #{key}
        LIMIT 1
    </select>

    <update id="setDeleted">
        UPDATE application_env
        SET deleted = #{deleted}
        WHERE id = #{id}
    </update>

</mapper>
