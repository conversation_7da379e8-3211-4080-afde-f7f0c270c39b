<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.UserEventLogDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.UserEventLogDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="event_classify" property="eventClassify"/>
        <result column="event_type" property="eventType"/>
        <result column="log_info" property="logInfo"/>
        <result column="params_json" property="paramsJson"/>
        <result column="exec_result" property="execResult"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, username, event_classify, event_type, log_info, params_json, exec_result, deleted, create_time
    </sql>

</mapper>
