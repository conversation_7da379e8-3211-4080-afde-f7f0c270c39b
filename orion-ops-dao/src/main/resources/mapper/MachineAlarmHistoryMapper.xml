<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.MachineAlarmHistoryDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.MachineAlarmHistoryDO">
        <id column="id" property="id"/>
        <result column="machine_id" property="machineId"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_value" property="alarmValue"/>
        <result column="alarm_time" property="alarmTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , machine_id, alarm_type, alarm_value, alarm_time, deleted, create_time, update_time
    </sql>

</mapper>
