<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.ApplicationRepositoryDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.ApplicationRepositoryDO">
        <id column="id" property="id"/>
        <result column="repo_name" property="repoName"/>
        <result column="repo_description" property="repoDescription"/>
        <result column="repo_type" property="repoType"/>
        <result column="repo_url" property="repoUrl"/>
        <result column="repo_username" property="repoUsername"/>
        <result column="repo_password" property="repoPassword"/>
        <result column="repo_private_token" property="repoPrivateToken"/>
        <result column="repo_status" property="repoStatus"/>
        <result column="repo_auth_type" property="repoAuthType"/>
        <result column="repo_token_type" property="repoTokenType"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, repo_name, repo_description, repo_type, repo_url, repo_username, repo_password, repo_private_token,
        repo_status, repo_auth_type, repo_token_type, deleted, create_time, update_time
    </sql>

    <select id="selectNameByIdList" resultMap="BaseResultMap">
        SELECT id, repo_name
        FROM application_repository
        WHERE id IN
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND deleted = 1
    </select>

    <select id="selectIdByNameList" resultMap="BaseResultMap">
        SELECT id, repo_name
        FROM application_repository
        WHERE repo_name IN
        <foreach collection="nameList" item="name" separator="," open="(" close=")">
            #{name}
        </foreach>
        AND deleted = 1
    </select>

</mapper>
