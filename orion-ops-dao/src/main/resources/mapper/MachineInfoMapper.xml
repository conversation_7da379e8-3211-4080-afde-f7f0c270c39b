<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.MachineInfoDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.MachineInfoDO">
        <id column="id" property="id"/>
        <result column="proxy_id" property="proxyId"/>
        <result column="machine_host" property="machineHost"/>
        <result column="ssh_port" property="sshPort"/>
        <result column="machine_name" property="machineName"/>
        <result column="machine_tag" property="machineTag"/>
        <result column="description" property="description"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="auth_type" property="authType"/>
        <result column="machine_status" property="machineStatus"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, proxy_id, machine_host, ssh_port, machine_name, machine_tag, description, username, password, auth_type, machine_status, deleted, create_time, update_time
    </sql>

    <update id="setId">
        UPDATE machine_info
        SET id = #{newId}
        WHERE id = #{oldId}
    </update>

    <update id="setProxyIdWithNull">
        UPDATE machine_info
        SET proxy_id = null
        WHERE proxy_id = #{proxyId}
    </update>

    <select id="selectIdByHost" resultType="java.lang.Long">
        SELECT id
        FROM machine_info
        WHERE machine_host LIKE CONCAT('%', #{host}, '%')
        AND deleted = 1
    </select>

    <select id="selectMachineName" resultType="java.lang.String">
        SELECT machine_name
        FROM machine_info
        WHERE id = #{id}
    </select>

    <select id="selectNameByIdList" resultMap="BaseResultMap">
        SELECT id, machine_name, machine_tag, machine_host
        FROM machine_info
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 1
    </select>

    <select id="selectIdByTagList" resultMap="BaseResultMap">
        SELECT id, machine_name, machine_tag, machine_host
        FROM machine_info
        WHERE machine_tag IN
        <foreach collection="tagList" item="tag" open="(" separator="," close=")">
            #{tag}
        </foreach>
        AND deleted = 1
    </select>

</mapper>
