<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.FileTransferLogDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.FileTransferLogDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="file_token" property="fileToken"/>
        <result column="transfer_type" property="transferType"/>
        <result column="machine_id" property="machineId"/>
        <result column="remote_file" property="remoteFile"/>
        <result column="local_file" property="localFile"/>
        <result column="file_size" property="fileSize"/>
        <result column="current_size" property="currentSize"/>
        <result column="now_progress" property="nowProgress"/>
        <result column="transfer_status" property="transferStatus"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, file_token, transfer_type, machine_id, remote_file, local_file, current_size, file_size, now_progress, transfer_status, deleted, create_time, update_time
    </sql>

</mapper>
