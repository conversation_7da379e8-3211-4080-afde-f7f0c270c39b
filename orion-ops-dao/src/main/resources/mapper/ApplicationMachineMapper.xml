<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.ApplicationMachineDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.ApplicationMachineDO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="profile_id" property="profileId"/>
        <result column="machine_id" property="machineId"/>
        <result column="release_id" property="releaseId"/>
        <result column="build_id" property="buildId"/>
        <result column="build_seq" property="buildSeq"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, profile_id, machine_id, release_id, build_id, build_seq, deleted, create_time, update_time
    </sql>

    <update id="updateAppVersion">
        UPDATE application_machine
        SET release_id = #{releaseId},
        build_id = #{buildId},
        build_seq = #{buildSeq}
        WHERE app_id = #{appId}
        AND profile_id = #{profileId}
        AND machine_id = #{machineId}
        AND deleted = 1
    </update>

</mapper>
