<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.ApplicationPipelineDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.ApplicationPipelineDO">
        <id column="id" property="id"/>
        <result column="profile_id" property="profileId"/>
        <result column="pipeline_name" property="pipelineName"/>
        <result column="description" property="description"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, profile_id, pipeline_name, description, deleted, create_time, update_time
    </sql>

</mapper>
