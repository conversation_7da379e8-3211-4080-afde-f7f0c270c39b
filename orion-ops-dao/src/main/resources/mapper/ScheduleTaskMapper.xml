<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.SchedulerTaskDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.SchedulerTaskDO">
        <id column="id" property="id"/>
        <result column="task_name" property="taskName"/>
        <result column="description" property="description"/>
        <result column="task_command" property="taskCommand"/>
        <result column="expression" property="expression"/>
        <result column="enable_status" property="enableStatus"/>
        <result column="lately_status" property="latelyStatus"/>
        <result column="serialize_type" property="serializeType"/>
        <result column="exception_handler" property="exceptionHandler"/>
        <result column="lately_schedule_time" property="latelyScheduleTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_name, description, task_command, expression, enable_status, lately_status, serialize_type, exception_handler, lately_schedule_time, deleted, create_time, update_time
    </sql>

</mapper>
