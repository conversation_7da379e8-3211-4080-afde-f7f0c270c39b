<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.ApplicationPipelineTaskDetailDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.ApplicationPipelineTaskDetailDO">
        <id column="id" property="id"/>
        <result column="pipeline_id" property="pipelineId"/>
        <result column="pipeline_detail_id" property="pipelineDetailId"/>
        <result column="task_id" property="taskId"/>
        <result column="rel_id" property="relId"/>
        <result column="app_id" property="appId"/>
        <result column="app_name" property="appName"/>
        <result column="app_tag" property="appTag"/>
        <result column="stage_type" property="stageType"/>
        <result column="stage_config" property="stageConfig"/>
        <result column="exec_status" property="execStatus"/>
        <result column="exec_start_time" property="execStartTime"/>
        <result column="exec_end_time" property="execEndTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, pipeline_id, pipeline_detail_id, task_id, rel_id, app_id, app_name, app_tag, stage_type, stage_config, exec_status, exec_start_time, exec_end_time, deleted, create_time, update_time
    </sql>

    <sql id="Status_Column_List">
        id, task_id, rel_id, exec_status, exec_start_time, exec_end_time
    </sql>

    <select id="selectStatusByTaskId" resultType="cn.orionsec.ops.entity.domain.ApplicationPipelineTaskDetailDO">
        SELECT
        <include refid="Status_Column_List"/>
        FROM application_pipeline_task_detail
        WHERE task_id = #{taskId}
    </select>

    <select id="selectStatusByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Status_Column_List"/>
        FROM application_pipeline_task_detail
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>
