<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.MachineSecretKeyDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.MachineSecretKeyDO">
        <id column="id" property="id"/>
        <result column="key_name" property="keyName"/>
        <result column="secret_key_path" property="secretKeyPath"/>
        <result column="password" property="password"/>
        <result column="description" property="description"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , key_name, secret_key_path, password, description, deleted, create_time, update_time
    </sql>

    <select id="selectIdByNameList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM machine_secret_key
        WHERE key_name IN
        <foreach collection="nameList" item="name" separator="," open="(" close=")">
            #{name}
        </foreach>
        AND deleted = 1
    </select>

</mapper>
