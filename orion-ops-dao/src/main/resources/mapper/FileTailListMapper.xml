<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.FileTailListDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.FileTailListDO">
        <id column="id" property="id"/>
        <result column="machine_id" property="machineId"/>
        <result column="alias_name" property="aliasName"/>
        <result column="file_path" property="filePath"/>
        <result column="file_charset" property="fileCharset"/>
        <result column="file_offset" property="fileOffset"/>
        <result column="tail_command" property="tailCommand"/>
        <result column="tail_mode" property="tailMode"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, machine_id, alias_name, file_path, file_charset, file_offset, tail_command, tail_mode, deleted, create_time, update_time
    </sql>

</mapper>
