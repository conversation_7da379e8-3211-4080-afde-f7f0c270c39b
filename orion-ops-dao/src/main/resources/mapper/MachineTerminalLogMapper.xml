<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.MachineTerminalLogDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.MachineTerminalLogDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="machine_id" property="machineId"/>
        <result column="machine_name" property="machineName"/>
        <result column="machine_host" property="machineHost"/>
        <result column="machine_tag" property="machineTag"/>
        <result column="access_token" property="accessToken"/>
        <result column="connected_time" property="connectedTime"/>
        <result column="disconnected_time" property="disconnectedTime"/>
        <result column="close_code" property="closeCode"/>
        <result column="screen_path" property="screenPath"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, username, machine_id, machine_name, machine_host, machine_tag, access_token, connected_time, disconnected_time, close_code, screen_path, create_time, update_time
    </sql>

</mapper>
