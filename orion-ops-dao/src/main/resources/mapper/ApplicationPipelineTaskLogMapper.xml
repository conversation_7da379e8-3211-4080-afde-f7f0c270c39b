<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.ApplicationPipelineTaskLogDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.ApplicationPipelineTaskLogDO">
        <id column="id" property="id"/>
        <result column="record_id" property="taskId"/>
        <result column="task_detail_id" property="taskDetailId"/>
        <result column="log_status" property="logStatus"/>
        <result column="stage_type" property="stageType"/>
        <result column="log_info" property="logInfo"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, task_detail_id, log_status, stage_type, log_info, deleted, create_time, update_time
    </sql>

</mapper>
