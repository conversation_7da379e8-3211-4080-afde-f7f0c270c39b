<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.ApplicationActionDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.ApplicationActionDO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="profile_id" property="profileId"/>
        <result column="action_name" property="actionName"/>
        <result column="action_type" property="actionType"/>
        <result column="stage_type" property="stageType"/>
        <result column="action_command" property="actionCommand"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="ConfigResultMap" type="cn.orionsec.ops.entity.dto.ApplicationActionConfigDTO">
        <result column="app_id" property="appId"/>
        <result column="build_stage_count" property="buildStageCount"/>
        <result column="release_stage_count" property="releaseStageCount"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, profile_id, action_name, action_type, stage_type, action_command, deleted, create_time, update_time
    </sql>

    <select id="getAppIsConfig" resultMap="ConfigResultMap">
        SELECT app_id,
        COUNT(stage_type = 10 OR NULL) build_stage_count,
        COUNT(stage_type = 20 OR NULL) release_stage_count
        FROM application_action
        WHERE profile_id = #{profileId}
        AND app_id IN
        <foreach collection="appIdList" item="appId" separator="," open="(" close=")">
            #{appId}
        </foreach>
        AND deleted = 1
        GROUP BY app_id
    </select>

</mapper>
