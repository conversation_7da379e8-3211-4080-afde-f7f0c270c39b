<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.HistoryValueSnapshotDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.HistoryValueSnapshotDO">
        <id column="id" property="id"/>
        <result column="value_id" property="valueId"/>
        <result column="operator_type" property="operatorType"/>
        <result column="value_type" property="valueType"/>
        <result column="before_value" property="beforeValue"/>
        <result column="after_value" property="afterValue"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, value_id, operator_type, value_type, before_value, after_value, update_user_id, update_user_name, create_time, update_time
    </sql>

</mapper>
