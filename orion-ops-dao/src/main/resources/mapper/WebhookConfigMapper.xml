<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.WebhookConfigDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.WebhookConfigDO">
        <id column="id" property="id"/>
        <result column="webhook_name" property="webhookName"/>
        <result column="webhook_url" property="webhookUrl"/>
        <result column="webhook_type" property="webhookType"/>
        <result column="webhook_config" property="webhookConfig"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , webhook_name, webhook_url, webhook_type, webhook_config, deleted, create_time, update_time
    </sql>

</mapper>
