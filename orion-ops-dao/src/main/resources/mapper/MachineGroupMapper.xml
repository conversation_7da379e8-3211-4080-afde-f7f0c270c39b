<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.MachineGroupDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.MachineGroupDO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="group_name" property="groupName"/>
        <result column="sort" property="sort"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , parent_id, group_name, sort, deleted, create_time, update_time
    </sql>

    <update id="incrementSort">
        UPDATE machine_group
        SET sort = sort + 1
        WHERE parent_id = #{parentId}
        <if test="greaterSort != null">
            AND sort > #{greaterSort}
        </if>
    </update>

    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT max(sort)
        FROM machine_group
        WHERE parent_id = #{parentId}
    </select>

</mapper>
