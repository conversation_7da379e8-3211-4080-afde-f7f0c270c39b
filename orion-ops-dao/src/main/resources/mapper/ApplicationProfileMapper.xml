<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.ApplicationProfileDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.ApplicationProfileDO">
        <id column="id" property="id"/>
        <result column="profile_name" property="profileName"/>
        <result column="profile_tag" property="profileTag"/>
        <result column="description" property="description"/>
        <result column="release_audit" property="releaseAudit"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, profile_name, profile_tag, description, release_audit, deleted, create_time, update_time
    </sql>

</mapper>
