<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.orionsec.ops.dao.MachineProxyDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.orionsec.ops.entity.domain.MachineProxyDO">
        <id column="id" property="id"/>
        <result column="proxy_host" property="proxyHost"/>
        <result column="proxy_port" property="proxyPort"/>
        <result column="proxy_username" property="proxyUsername"/>
        <result column="proxy_password" property="proxyPassword"/>
        <result column="description" property="description"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, proxy_host, proxy_port, proxy_username, proxy_password, description, deleted, create_time, update_time
    </sql>

</mapper>
