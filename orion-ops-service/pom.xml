<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.orionsec.ops</groupId>
        <artifactId>orion-ops-api</artifactId>
        <version>1.3.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <name>orion-ops-service</name>
    <artifactId>orion-ops-service</artifactId>
    <modelVersion>4.0.0</modelVersion>

    <dependencies>
        <!-- common -->
        <dependency>
            <groupId>cn.orionsec.ops</groupId>
            <artifactId>orion-ops-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- model -->
        <dependency>
            <groupId>cn.orionsec.ops</groupId>
            <artifactId>orion-ops-model</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- dao -->
        <dependency>
            <groupId>cn.orionsec.ops</groupId>
            <artifactId>orion-ops-dao</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- web socket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>

        <!-- tests -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <!-- ding -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
        </dependency>
    </dependencies>

</project>
