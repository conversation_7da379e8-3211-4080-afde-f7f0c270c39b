/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   <PERSON>ahang Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.handler.app.pipeline.stage;

import cn.orionsec.kit.lang.able.Executable;
import cn.orionsec.kit.lang.utils.Exceptions;
import cn.orionsec.ops.constant.app.PipelineDetailStatus;
import cn.orionsec.ops.constant.app.StageType;
import cn.orionsec.ops.entity.domain.ApplicationPipelineTaskDO;
import cn.orionsec.ops.entity.domain.ApplicationPipelineTaskDetailDO;

/**
 * 流水线阶段处理器接口
 *
 * <AUTHOR> Li
 * @version 1.0.0
 * @since 2022/4/15 14:56
 */
public interface IStageHandler extends Executable {

    /**
     * 停止执行
     */
    void terminate();

    /**
     * 跳过执行
     */
    void skip();

    /**
     * 获取状态
     *
     * @return status
     */
    PipelineDetailStatus getStatus();

    /**
     * 获取阶段处理器
     *
     * @param task   task
     * @param detail detail
     * @return 阶段处理器
     */
    static IStageHandler with(ApplicationPipelineTaskDO task, ApplicationPipelineTaskDetailDO detail) {
        StageType stageType = StageType.of(detail.getStageType());
        switch (stageType) {
            case BUILD:
                return new BuildStageHandler(task, detail);
            case RELEASE:
                return new ReleaseStageHandler(task, detail);
            default:
                throw Exceptions.argument();
        }
    }

}
