<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.orionsec.ops</groupId>
        <artifactId>orion-ops-api</artifactId>
        <version>1.3.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <name>orion-ops-mapping</name>
    <artifactId>orion-ops-mapping</artifactId>
    <modelVersion>4.0.0</modelVersion>

    <dependencies>
        <!-- dao -->
        <dependency>
            <groupId>cn.orionsec.ops</groupId>
            <artifactId>orion-ops-dao</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- model -->
        <dependency>
            <groupId>cn.orionsec.ops</groupId>
            <artifactId>orion-ops-model</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- data -->
        <dependency>
            <groupId>cn.orionsec.ops</groupId>
            <artifactId>orion-ops-data</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>
