/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   <PERSON><PERSON>g Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.mapping.machine;

import cn.orionsec.kit.lang.utils.convert.TypeStore;
import cn.orionsec.ops.entity.domain.MachineProxyDO;
import cn.orionsec.ops.entity.vo.machine.MachineProxyVO;

/**
 * 机器代理 对象转换器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/8/10 18:10
 */
public class MachineProxyConversion {

    static {
        TypeStore.STORE.register(MachineProxyDO.class, MachineProxyVO.class, p -> {
            MachineProxyVO vo = new MachineProxyVO();
            vo.setId(p.getId());
            vo.setHost(p.getProxyHost());
            vo.setPort(p.getProxyPort());
            vo.setUsername(p.getProxyUsername());
            vo.setType(p.getProxyType());
            vo.setDescription(p.getDescription());
            vo.setCreateTime(p.getCreateTime());
            return vo;
        });
    }

}
