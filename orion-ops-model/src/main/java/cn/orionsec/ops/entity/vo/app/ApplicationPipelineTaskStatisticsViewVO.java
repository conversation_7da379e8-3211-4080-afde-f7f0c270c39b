/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   Jiahang Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.entity.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 应用流水线统计分析操作响应
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @since 2022/5/10 11:13
 */
@Data
@ApiModel(value = "应用流水线统计分析操作响应")
public class ApplicationPipelineTaskStatisticsViewVO {

    @ApiModelProperty(value = "成功平均执行时长毫秒")
    private Long avgUsed;

    @ApiModelProperty(value = "成功平均执行时长")
    private String avgUsedInterval;

    @ApiModelProperty(value = "流水线操作")
    private List<ApplicationPipelineStatisticsDetailVO> details;

    @ApiModelProperty(value = "流水线执行记录")
    private List<ApplicationPipelineTaskStatisticsTaskVO> pipelineTaskList;

}
