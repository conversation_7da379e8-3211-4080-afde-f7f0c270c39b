/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   Jiahang Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.entity.dto.sftp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 文件传输进度
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/8/2 11:32
 */
@Data
@AllArgsConstructor
@ApiModel(value = "文件传输进度")
public class FileTransferNotifyProgressDTO {

    @ApiModelProperty(value = "速度")
    private String rate;

    @ApiModelProperty(value = "当前位置")
    private String current;

    @ApiModelProperty(value = "进度")
    private String progress;

}
