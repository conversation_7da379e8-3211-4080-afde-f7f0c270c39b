/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   Jiahang Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.entity.vo.alarm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 报警组响应
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @since 2022/8/25 17:35
 */
@Data
@ApiModel(value = "报警组响应")
public class AlarmGroupVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "组名称")
    private String name;

    @ApiModelProperty(value = "组描述")
    private String description;

    @ApiModelProperty(value = "组员名称")
    private List<AlarmGroupUserVO> groupUsers;

    @ApiModelProperty(value = "报警组员id")
    private List<Long> userIdList;

    @ApiModelProperty(value = "报警通知方式")
    private List<Long> notifyIdList;

}
