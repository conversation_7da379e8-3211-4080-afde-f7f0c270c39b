/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   Jiahang Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.entity.vo.sftp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * sftp 打开连接响应
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @since 2021/6/23 18:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "打开连接响应")
public class FileOpenVO extends FileListVO {

    @ApiModelProperty(value = "根目录")
    private String home;

    @ApiModelProperty(value = "sessionToken")
    private String sessionToken;

    @ApiModelProperty(value = "编码格式")
    private String charset;

}
