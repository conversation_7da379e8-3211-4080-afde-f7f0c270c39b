/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   Jiahang Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.entity.vo.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 站内信消息轮询响应
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/3/28 16:15
 */
@Data
@ApiModel(value = "站内信消息轮询响应")
public class WebSideMessagePollVO {

    @ApiModelProperty(value = "未读数量")
    private Integer unreadCount;

    @ApiModelProperty(value = "最大id")
    private Long maxId;

    @ApiModelProperty(value = "最新消息")
    private List<WebSideMessageVO> newMessages;

}
