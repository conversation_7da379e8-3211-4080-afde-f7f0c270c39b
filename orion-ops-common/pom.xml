<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>cn.orionsec.ops</groupId>
        <artifactId>orion-ops-api</artifactId>
        <version>1.3.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <name>orion-ops-common</name>
    <artifactId>orion-ops-common</artifactId>
    <modelVersion>4.0.0</modelVersion>

    <dependencies>
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- orion-kit -->
        <dependency>
            <groupId>cn.orionsec.kit</groupId>
            <artifactId>orion-all</artifactId>
        </dependency>

        <!-- web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>

</project>
