/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   Jiahang Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.constant.event;

/**
 * 事件key
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @since 2022/1/22 19:48
 */
public class EventKeys {

    private EventKeys() {
    }

    /**
     * 内置用户id
     */
    public static final String INNER_USER_ID = "_USER_ID_";

    /**
     * 内置用户名称
     */
    public static final String INNER_USER_NAME = "_USER_NAME_";

    /**
     * 内置是否保存
     */
    public static final String INNER_SAVE = "_SAVE_";

    /**
     * 内置模板
     */
    public static final String INNER_TEMPLATE = "_TEMPLATE_";

    /**
     * 请求序列
     */
    public static final String INNER_REQUEST_SEQ = "_REQUEST_SEQ_";

    /**
     * 请求 UA
     */
    public static final String INNER_REQUEST_USER_AGENT = "userAgent";

    /**
     * 请求 ip
     */
    public static final String INNER_REQUEST_IP = "ip";

    /**
     * 请求时间
     */
    public static final String INNER_REQUEST_TIME = "requestTime";

    /**
     * id
     */
    public static final String ID = "id";

    /**
     * id list
     */
    public static final String ID_LIST = "idList";

    /**
     * machine id
     */
    public static final String MACHINE_ID = "machineId";

    /**
     * machine id list
     */
    public static final String MACHINE_ID_LIST = "machineIdList";

    /**
     * app id
     */
    public static final String APP_ID = "appId";

    /**
     * profile id
     */
    public static final String PROFILE_ID = "profileId";

    /**
     * detail id
     */
    public static final String DETAIL_ID = "detailId";

    /**
     * type
     */
    public static final String TYPE = "type";

    /**
     * value
     */
    public static final String VALUE = "value";

    /**
     * token
     */
    public static final String TOKEN = "token";

    /**
     * 名称
     */
    public static final String NAME = "name";

    /**
     * 主机
     */
    public static final String HOST = "host";

    /**
     * 用户名
     */
    public static final String USERNAME = "username";

    /**
     * 源
     */
    public static final String SOURCE = "source";

    /**
     * 目标
     */
    public static final String TARGET = "target";

    /**
     * 数量
     */
    public static final String COUNT = "count";

    /**
     * 环境数量
     */
    public static final String ENV_COUNT = "envCount";

    /**
     * 机器数量
     */
    public static final String MACHINE_COUNT = "machineCount";

    /**
     * 机器名称
     */
    public static final String MACHINE_NAME = "machineName";

    /**
     * 环境数量
     */
    public static final String PROFILE_COUNT = "profileCount";

    /**
     * 路径
     */
    public static final String PATH = "path";

    /**
     * 路径
     */
    public static final String PATHS = "paths";

    /**
     * 操作
     */
    public static final String OPERATOR = "operator";

    /**
     * 序列
     */
    public static final String SEQ = "seq";

    /**
     * 构建序列
     */
    public static final String BUILD_SEQ = "buildSeq";

    /**
     * 环境名称
     */
    public static final String PROFILE_NAME = "profileName";

    /**
     * 应用名称
     */
    public static final String APP_NAME = "appName";

    /**
     * 标题
     */
    public static final String TITLE = "title";

    /**
     * 阶段
     */
    public static final String STAGE = "stage";

    /**
     * 系统
     */
    public static final String SYSTEM = "system";

    /**
     * before
     */
    public static final String BEFORE = "before";

    /**
     * after
     */
    public static final String AFTER = "after";

    /**
     * key
     */
    public static final String KEY = "key";

    /**
     * label
     */
    public static final String LABEL = "label";

    /**
     * time
     */
    public static final String TIME = "time";

    /**
     * details
     */
    public static final String DETAILS = "details";

    /**
     * 流水线id
     */
    public static final String PIPELINE_ID = "pipelineId";

    /**
     * 是否导出密码
     */
    public static final String EXPORT_PASSWORD = "exportPassword";

    /**
     * 保护密码
     */
    public static final String PROTECT = "protect";

    /**
     * 分类
     */
    public static final String CLASSIFY = "classify";

    /**
     * 用户id
     */
    public static final String USER_ID = "userId";

    /**
     * 状态
     */
    public static final String STATUS = "status";

    /**
     * 是否为自动续签登录
     */
    public static final String REFRESH_LOGIN = "refreshLogin";

    /**
     * 导出类型
     */
    public static final String EXPORT_TYPE = "exportType";

}
