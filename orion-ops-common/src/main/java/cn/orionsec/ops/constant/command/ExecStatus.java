/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   <PERSON><PERSON>g Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.constant.command;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 执行状态
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @since 2021/6/4 18:20
 */
@AllArgsConstructor
@Getter
public enum ExecStatus {

    /**
     * 10未开始
     */
    WAITING(10),

    /**
     * 20执行中
     */
    RUNNABLE(20),

    /**
     * 30执行完成
     */
    COMPLETE(30),

    /**
     * 40执行异常
     */
    EXCEPTION(40),

    /**
     * 50执行终止
     */
    TERMINATED(50),

    ;

    private final Integer status;

    public static ExecStatus of(Integer status) {
        if (status == null) {
            return null;
        }
        for (ExecStatus value : values()) {
            if (value.status.equals(status)) {
                return value;
            }
        }
        return null;
    }

}
