/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   <PERSON>ahang Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.constant.app;

import lombok.Getter;

/**
 * 产物传输方式 (sftp/scp)
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @since 2022/4/26 22:16
 */
@Getter
public enum TransferMode {

    /**
     * scp
     */
    SCP,

    /**
     * sftp
     */
    SFTP,

    ;

    TransferMode() {
        this.value = name().toLowerCase();
    }

    private final String value;

    public static TransferMode of(String value) {
        if (value == null) {
            return SCP;
        }
        for (TransferMode type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return SCP;
    }

}
