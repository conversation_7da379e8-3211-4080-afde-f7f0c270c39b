/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   Jiahang Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.constant.machine;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 机器报警类型
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @since 2022/8/26 18:18
 */
@Getter
@AllArgsConstructor
public enum MachineAlarmType {

    /**
     * cpu 使用率
     */
    CPU_USAGE(10, "CPU使用率"),

    /**
     * 内存使用率
     */
    MEMORY_USAGE(20, "内存使用率"),

    ;

    private final Integer type;

    private final String label;

    public static MachineAlarmType of(Integer type) {
        if (type == null) {
            return null;
        }
        for (MachineAlarmType value : values()) {
            if (value.type.equals(type)) {
                return value;
            }
        }
        return null;
    }

}
