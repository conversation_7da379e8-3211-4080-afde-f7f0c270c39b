/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   Jiahang Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.constant;

/**
 * 中文常量
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @since 2022/5/29 16:25
 */
public class CnConst {

    private CnConst() {
    }

    public static final String ENABLE = "启用";

    public static final String DISABLE = "停用";

    public static final String RESOLVE = "通过";

    public static final String REJECT = "驳回";

    public static final String SUCCESS = "成功";

    public static final String FAILURE = "失败";

    public static final String OPEN = "开启";

    public static final String CLOSE = "关闭";

    public static final String YES = "是";

    public static final String NO = "否";

    public static final String APP = "应用";

    public static final String RELEASE = "发布";

    public static final String PASSWORD = "密码";

    public static final String TOKEN = "令牌";

    public static final String SECRET_KEY = "独立密钥";

    public static final String READ = "已读";

    public static final String UNREAD = "未读";

    public static final String ORION_OPS_AUTHOR = "李佳航";

    public static final String UNKNOWN = "未知";

    public static final String INTRANET_IP = "内网IP";

    public static final String INSTALL = "安装";

    public static final String UPGRADE = "升级";

}
