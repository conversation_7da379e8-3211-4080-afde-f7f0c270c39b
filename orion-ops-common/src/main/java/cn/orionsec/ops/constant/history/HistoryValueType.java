/*
 * Copyright (c) 2021 - present <PERSON><PERSON><PERSON> All rights reserved.
 *
 *   https://ops.orionsec.cn
 *
 * Members:
 *   Jiahang Li - <EMAIL> - author
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.orionsec.ops.constant.history;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 历史值类型
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @since 2021/6/3 14:00
 */
@AllArgsConstructor
@Getter
public enum HistoryValueType {

    /**
     * 机器环境变量
     */
    MACHINE_ENV(10),

    /**
     * 应用环境变量
     */
    APP_ENV(20),

    /**
     * 系统环境变量
     */
    SYSTEM_ENV(30),

    ;

    private final Integer type;

    public static HistoryValueType of(Integer type) {
        if (type == null) {
            return null;
        }
        for (HistoryValueType value : values()) {
            if (type.equals(value.type)) {
                return value;
            }
        }
        return null;
    }

}
